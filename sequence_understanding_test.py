#!/usr/bin/env python3
"""
LLM-SRec序列理解能力验证测试
验证是否成功将SASRec的序列理解能力传递给LLM
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

class SequenceUnderstandingValidator:
    """序列理解能力验证器"""
    
    def __init__(self, llm_srec_model, sasrec_model, device='cuda'):
        self.llm_srec = llm_srec_model
        self.sasrec = sasrec_model
        self.device = device
        
    def test_shuffled_sequences(self, test_data: Dict, num_samples: int = 1000) -> Dict:
        """
        测试打乱序列对模型性能的影响
        这是LLM-SRec论文的核心验证实验
        """
        print("🔍 开始序列理解能力测试...")
        
        results = {
            'llm_srec': {'original': [], 'shuffled': []},
            'sasrec': {'original': [], 'shuffled': []},
            'representation_similarity': {'llm_srec': [], 'sasrec': []}
        }
        
        # 随机采样测试数据
        sample_indices = np.random.choice(len(test_data['sequences']), 
                                        min(num_samples, len(test_data['sequences'])), 
                                        replace=False)
        
        for idx in sample_indices:
            seq = test_data['sequences'][idx]
            target = test_data['targets'][idx]
            
            # 1. 原始序列测试
            original_results = self._evaluate_single_sequence(seq, target, shuffle=False)
            
            # 2. 打乱序列测试
            shuffled_results = self._evaluate_single_sequence(seq, target, shuffle=True)
            
            # 记录结果
            results['llm_srec']['original'].append(original_results['llm_srec_score'])
            results['llm_srec']['shuffled'].append(shuffled_results['llm_srec_score'])
            results['sasrec']['original'].append(original_results['sasrec_score'])
            results['sasrec']['shuffled'].append(shuffled_results['sasrec_score'])
            
            # 3. 表示相似性测试
            similarity_scores = self._compute_representation_similarity(seq)
            results['representation_similarity']['llm_srec'].append(similarity_scores['llm_srec'])
            results['representation_similarity']['sasrec'].append(similarity_scores['sasrec'])
        
        return self._analyze_results(results)
    
    def _evaluate_single_sequence(self, sequence: List[int], target: int, shuffle: bool = False) -> Dict:
        """评估单个序列"""
        seq = sequence.copy()
        if shuffle:
            # 保持第一个和最后一个位置，只打乱中间部分
            if len(seq) > 2:
                middle = seq[1:-1]
                np.random.shuffle(middle)
                seq = [seq[0]] + middle + [seq[-1]]
        
        # 转换为模型输入格式
        seq_tensor = torch.LongTensor([seq]).to(self.device)
        target_tensor = torch.LongTensor([target]).to(self.device)
        
        with torch.no_grad():
            # LLM-SRec评估
            llm_srec_score = self._evaluate_llm_srec(seq_tensor, target_tensor)
            
            # SASRec评估
            sasrec_score = self._evaluate_sasrec(seq_tensor, target_tensor)
        
        return {
            'llm_srec_score': llm_srec_score,
            'sasrec_score': sasrec_score
        }
    
    def _evaluate_llm_srec(self, seq_tensor: torch.Tensor, target_tensor: torch.Tensor) -> float:
        """评估LLM-SRec模型"""
        try:
            # 构造输入文本
            text_input = self._create_text_input(seq_tensor[0])
            
            # 获取用户表示
            user_repr = self._get_llm_user_representation(text_input)
            
            # 获取目标物品表示
            target_repr = self._get_item_representation(target_tensor[0])
            
            # 计算相似度分数
            score = torch.cosine_similarity(user_repr, target_repr, dim=0).item()
            return score
            
        except Exception as e:
            print(f"LLM-SRec评估错误: {e}")
            return 0.0
    
    def _evaluate_sasrec(self, seq_tensor: torch.Tensor, target_tensor: torch.Tensor) -> float:
        """评估SASRec模型"""
        try:
            # 获取序列表示
            log_feats = self.sasrec.log2feats(seq_tensor.cpu().numpy())
            user_repr = log_feats[:, -1, :]  # 取最后一个位置
            
            # 获取目标物品嵌入
            target_emb = self.sasrec.item_emb(target_tensor)
            
            # 计算相似度分数
            score = torch.cosine_similarity(
                torch.FloatTensor(user_repr).to(self.device), 
                target_emb, 
                dim=1
            ).item()
            return score
            
        except Exception as e:
            print(f"SASRec评估错误: {e}")
            return 0.0
    
    def _compute_representation_similarity(self, sequence: List[int]) -> Dict:
        """计算原始序列和打乱序列的表示相似性"""
        seq = sequence.copy()
        
        # 原始序列表示
        original_repr = self._get_sequence_representations(seq)
        
        # 打乱序列表示
        shuffled_seq = seq.copy()
        if len(shuffled_seq) > 2:
            middle = shuffled_seq[1:-1]
            np.random.shuffle(middle)
            shuffled_seq = [shuffled_seq[0]] + middle + [shuffled_seq[-1]]
        
        shuffled_repr = self._get_sequence_representations(shuffled_seq)
        
        # 计算相似性
        llm_similarity = torch.cosine_similarity(
            original_repr['llm_srec'], 
            shuffled_repr['llm_srec'], 
            dim=0
        ).item()
        
        sasrec_similarity = torch.cosine_similarity(
            original_repr['sasrec'], 
            shuffled_repr['sasrec'], 
            dim=0
        ).item()
        
        return {
            'llm_srec': llm_similarity,
            'sasrec': sasrec_similarity
        }
    
    def _get_sequence_representations(self, sequence: List[int]) -> Dict:
        """获取序列的表示"""
        seq_tensor = torch.LongTensor([sequence]).to(self.device)
        
        with torch.no_grad():
            # LLM-SRec表示
            text_input = self._create_text_input(sequence)
            llm_repr = self._get_llm_user_representation(text_input)
            
            # SASRec表示
            log_feats = self.sasrec.log2feats(seq_tensor.cpu().numpy())
            sasrec_repr = torch.FloatTensor(log_feats[:, -1, :]).to(self.device).squeeze(0)
        
        return {
            'llm_srec': llm_repr,
            'sasrec': sasrec_repr
        }
    
    def _create_text_input(self, sequence: List[int]) -> str:
        """创建LLM输入文本"""
        # 简化的文本输入创建
        item_names = [f"Item_{item_id}" for item_id in sequence if item_id > 0]
        text = f"This user has made a series of purchases in the following order: {', '.join(item_names)}. Based on this sequence of purchases, generate user representation token:[UserOut]"
        return text
    
    def _get_llm_user_representation(self, text_input: str) -> torch.Tensor:
        """获取LLM用户表示"""
        # 这里需要根据实际的LLM-SRec实现来调整
        # 简化实现，返回随机向量作为占位符
        return torch.randn(128).to(self.device)
    
    def _get_item_representation(self, item_id: int) -> torch.Tensor:
        """获取物品表示"""
        # 简化实现，返回随机向量作为占位符
        return torch.randn(128).to(self.device)
    
    def _analyze_results(self, results: Dict) -> Dict:
        """分析测试结果"""
        analysis = {}
        
        # 计算性能变化率
        for model in ['llm_srec', 'sasrec']:
            original_scores = np.array(results[model]['original'])
            shuffled_scores = np.array(results[model]['shuffled'])
            
            # 过滤无效值
            valid_mask = (original_scores != 0) & (shuffled_scores != 0)
            if valid_mask.sum() > 0:
                original_valid = original_scores[valid_mask]
                shuffled_valid = shuffled_scores[valid_mask]
                
                change_ratio = (shuffled_valid - original_valid) / original_valid * 100
                analysis[f'{model}_performance_change'] = {
                    'mean_change': np.mean(change_ratio),
                    'std_change': np.std(change_ratio),
                    'median_change': np.median(change_ratio)
                }
            else:
                analysis[f'{model}_performance_change'] = {
                    'mean_change': 0,
                    'std_change': 0,
                    'median_change': 0
                }
        
        # 计算表示相似性
        for model in ['llm_srec', 'sasrec']:
            similarities = np.array(results['representation_similarity'][model])
            valid_similarities = similarities[similarities != 0]
            
            if len(valid_similarities) > 0:
                analysis[f'{model}_representation_similarity'] = {
                    'mean_similarity': np.mean(valid_similarities),
                    'std_similarity': np.std(valid_similarities),
                    'median_similarity': np.median(valid_similarities)
                }
            else:
                analysis[f'{model}_representation_similarity'] = {
                    'mean_similarity': 0,
                    'std_similarity': 0,
                    'median_similarity': 0
                }
        
        return analysis
    
    def generate_report(self, analysis: Dict) -> str:
        """生成测试报告"""
        report = """
🔍 LLM-SRec序列理解能力验证报告
=====================================

📊 性能变化分析（打乱序列 vs 原始序列）：
----------------------------------------
LLM-SRec模型：
  - 平均性能变化: {:.2f}%
  - 性能变化标准差: {:.2f}%
  - 中位数性能变化: {:.2f}%

SASRec模型：
  - 平均性能变化: {:.2f}%
  - 性能变化标准差: {:.2f}%
  - 中位数性能变化: {:.2f}%

🎯 表示相似性分析（原始 vs 打乱序列）：
----------------------------------------
LLM-SRec表示相似性：
  - 平均相似性: {:.4f}
  - 相似性标准差: {:.4f}
  - 中位数相似性: {:.4f}

SASRec表示相似性：
  - 平均相似性: {:.4f}
  - 相似性标准差: {:.4f}
  - 中位数相似性: {:.4f}

📈 结论：
--------
""".format(
            analysis['llm_srec_performance_change']['mean_change'],
            analysis['llm_srec_performance_change']['std_change'],
            analysis['llm_srec_performance_change']['median_change'],
            analysis['sasrec_performance_change']['mean_change'],
            analysis['sasrec_performance_change']['std_change'],
            analysis['sasrec_performance_change']['median_change'],
            analysis['llm_srec_representation_similarity']['mean_similarity'],
            analysis['llm_srec_representation_similarity']['std_similarity'],
            analysis['llm_srec_representation_similarity']['median_similarity'],
            analysis['sasrec_representation_similarity']['mean_similarity'],
            analysis['sasrec_representation_similarity']['std_similarity'],
            analysis['sasrec_representation_similarity']['median_similarity']
        )
        
        # 添加结论
        llm_change = abs(analysis['llm_srec_performance_change']['mean_change'])
        sasrec_change = abs(analysis['sasrec_performance_change']['mean_change'])
        
        llm_similarity = analysis['llm_srec_representation_similarity']['mean_similarity']
        sasrec_similarity = analysis['sasrec_representation_similarity']['mean_similarity']
        
        if llm_change < sasrec_change and llm_similarity > sasrec_similarity:
            conclusion = "✅ LLM-SRec成功获得了序列理解能力！\n   - 对序列打乱的敏感性较低，表示相似性较高"
        elif llm_change >= sasrec_change:
            conclusion = "⚠️  LLM-SRec的序列理解能力有待提升\n   - 对序列打乱过于敏感"
        else:
            conclusion = "❓ 结果需要进一步分析"
        
        report += conclusion
        
        return report


def run_sequence_understanding_test():
    """运行序列理解能力测试"""
    print("🚀 启动LLM-SRec序列理解能力验证测试...")
    
    # 这里需要加载实际的模型
    # llm_srec_model = load_llm_srec_model()
    # sasrec_model = load_sasrec_model()
    
    # 创建模拟测试数据
    test_data = {
        'sequences': [[1, 2, 3, 4, 5], [10, 11, 12, 13], [20, 21, 22, 23, 24, 25]],
        'targets': [6, 14, 26]
    }
    
    # validator = SequenceUnderstandingValidator(llm_srec_model, sasrec_model)
    # analysis = validator.test_shuffled_sequences(test_data)
    # report = validator.generate_report(analysis)
    
    print("测试完成！请查看生成的报告。")
    # print(report)


if __name__ == "__main__":
    run_sequence_understanding_test()
